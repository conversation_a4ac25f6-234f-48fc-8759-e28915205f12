package cn.hanyi.survey.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 行政区划查询配置类
 */
@Configuration
@ConfigurationProperties(prefix = "admin.division")
public class AdminDivisionConfig {

    /**
     * Shapefile文件路径
     */
    private String shapefilePath = "/data/shapefiles/district.shp";

    /**
     * 字符集编码
     */
    private String charset = "UTF-8";

    /**
     * 是否启用空间索引
     */
    private boolean enableSpatialIndex = true;

    /**
     * 索引构建超时时间（毫秒）
     */
    private long indexBuildTimeout = 30000;

    /**
     * 是否在启动时预构建索引
     */
    private boolean prebuildIndex = true;

    public String getShapefilePath() {
        return shapefilePath;
    }

    public void setShapefilePath(String shapefilePath) {
        this.shapefilePath = shapefilePath;
    }

    public String getCharset() {
        return charset;
    }

    public void setCharset(String charset) {
        this.charset = charset;
    }

    public boolean isEnableSpatialIndex() {
        return enableSpatialIndex;
    }

    public void setEnableSpatialIndex(boolean enableSpatialIndex) {
        this.enableSpatialIndex = enableSpatialIndex;
    }

    public long getIndexBuildTimeout() {
        return indexBuildTimeout;
    }

    public void setIndexBuildTimeout(long indexBuildTimeout) {
        this.indexBuildTimeout = indexBuildTimeout;
    }

    public boolean isPrebuildIndex() {
        return prebuildIndex;
    }

    public void setPrebuildIndex(boolean prebuildIndex) {
        this.prebuildIndex = prebuildIndex;
    }
}
