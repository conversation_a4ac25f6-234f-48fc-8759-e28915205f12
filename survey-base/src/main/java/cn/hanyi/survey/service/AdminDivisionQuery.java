package cn.hanyi.survey.service;

import org.geotools.data.DataStore;
import org.geotools.data.DataStoreFinder;
import org.geotools.data.simple.SimpleFeatureCollection;
import org.geotools.data.simple.SimpleFeatureSource;
import org.geotools.factory.CommonFactoryFinder;
import org.geotools.geometry.jts.JTSFactoryFinder;
import org.locationtech.jts.geom.*;
import org.locationtech.jts.index.strtree.STRtree;
import org.opengis.feature.simple.SimpleFeature;
import org.opengis.filter.Filter;
import org.opengis.filter.FilterFactory2;

import java.io.File;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 使用GeoTools和ChinaAdminDivisonSHP数据通过经纬度查询省市区
 * 使用STRtree空间索引优化查询性能
 */
public class AdminDivisionQuery {

    private SimpleFeatureSource featureSource;
    private GeometryFactory geometryFactory;
    private FilterFactory2 filterFactory;
    private DataStore dataStore;
    private STRtree spatialIndex;
    private boolean indexBuilt = false;

    public AdminDivisionQuery(String shapefilePath) throws Exception {
        this(shapefilePath, "UTF-8");
    }

    public AdminDivisionQuery(String shapefilePath, String charset) throws Exception {
        // 1. 初始化几何和过滤器工厂
        geometryFactory = JTSFactoryFinder.getGeometryFactory();
        filterFactory = CommonFactoryFinder.getFilterFactory2();

        // 2. 加载Shapefile数据源
        File file = new File(shapefilePath);
        if (!file.exists()) {
            throw new RuntimeException("Shapefile not found: " + shapefilePath);
        }

        // 使用DataStore参数方式设置字符集，解决中文乱码问题
        Map<String, Object> params = new HashMap<>();
        params.put("url", file.toURI().toURL());
        if (charset != null && !charset.isEmpty()) {
            params.put("charset", charset);
            System.out.println("使用字符集: " + charset);
        }

        this.dataStore = DataStoreFinder.getDataStore(params);
        if (this.dataStore == null) {
            throw new RuntimeException("无法打开 Shapefile: " + shapefilePath);
        }

        String typeName = this.dataStore.getTypeNames()[0];
        this.featureSource = this.dataStore.getFeatureSource(typeName);

        // 3. 初始化空间索引
        this.spatialIndex = new STRtree();
    }

    /**
     * 构建空间索引
     * 将所有要素的几何边界框加入STRtree索引中
     */
    private void buildSpatialIndex() throws Exception {
        if (indexBuilt) {
            return;
        }

        System.out.println("正在构建空间索引...");
        long startTime = System.currentTimeMillis();

        SimpleFeatureCollection features = featureSource.getFeatures();
        try (org.geotools.data.simple.SimpleFeatureIterator iterator = features.features()) {
            while (iterator.hasNext()) {
                SimpleFeature feature = iterator.next();
                Geometry geometry = (Geometry) feature.getDefaultGeometry();
                if (geometry != null) {
                    Envelope envelope = geometry.getEnvelopeInternal();
                    spatialIndex.insert(envelope, feature);
                }
            }
        }

        spatialIndex.build();
        indexBuilt = true;

        long endTime = System.currentTimeMillis();
        System.out.println("空间索引构建完成，耗时: " + (endTime - startTime) + "ms");
    }

    /**
     * 根据经纬度查询行政区划信息（使用空间索引优化）
     *
     * @param lng 经度
     * @param lat 纬度
     * @return 包含行政区划信息的SimpleFeature，如果未找到则返回null
     */
    public SimpleFeature queryByCoordinate(double lng, double lat) throws Exception {
        // 确保空间索引已构建
        buildSpatialIndex();

        // 创建查询点
        Point point = geometryFactory.createPoint(new Coordinate(lng, lat));

        // 使用空间索引进行快速预筛选
        List<SimpleFeature> candidates = spatialIndex.query(point.getEnvelopeInternal());

        // 在候选要素中进行精确的几何包含测试
        for (SimpleFeature candidate : candidates) {
            Geometry geometry = (Geometry) candidate.getDefaultGeometry();
            if (geometry != null && geometry.contains(point)) {
                return candidate;
            }
        }

        return null;
    }

    /**
     * 根据经纬度查询行政区划信息（传统方法，用于性能对比）
     *
     * @param lng 经度
     * @param lat 纬度
     * @return 包含行政区划信息的SimpleFeature，如果未找到则返回null
     */
    public SimpleFeature queryByCoordinateTraditional(double lng, double lat) throws Exception {
        // 创建查询点
        Point point = geometryFactory.createPoint(new Coordinate(lng, lat));

        // 创建空间过滤器：查询包含该点的要素
        Filter filter = filterFactory.contains(filterFactory.property(featureSource.getSchema().getGeometryDescriptor().getLocalName()),
                filterFactory.literal(point));

        // 执行查询
        SimpleFeatureCollection features = featureSource.getFeatures(filter);

        // 获取结果（假设点只落在一个行政区划内）
        SimpleFeature feature = null;
        try (org.geotools.data.simple.SimpleFeatureIterator iterator = features.features()) {
            if (iterator.hasNext()) {
                feature = iterator.next();
            }
        }
        return feature;
    }

    /**
     * 批量查询多个坐标点（使用空间索引优化）
     *
     * @param coordinates 坐标点数组，每个元素包含[lng, lat]
     * @return 查询结果数组，与输入坐标一一对应
     */
    public SimpleFeature[] batchQuery(double[][] coordinates) throws Exception {
        buildSpatialIndex();

        SimpleFeature[] results = new SimpleFeature[coordinates.length];
        for (int i = 0; i < coordinates.length; i++) {
            results[i] = queryByCoordinate(coordinates[i][0], coordinates[i][1]);
        }
        return results;
    }


    /**
     * 释放资源
     */
    public void dispose() {
        if (dataStore != null) {
            dataStore.dispose();
        }
        if (spatialIndex != null) {
            // STRtree没有显式的dispose方法，但可以清空引用
            spatialIndex = null;
            indexBuilt = false;
        }
    }

    /**
     * 打印查询结果的特征属性
     *
     * @param feature 查询返回的特征
     */
    public void printFeatureInfo(SimpleFeature feature) {
        if (feature == null) {
            System.out.println("未找到对应的行政区划。");
            return;
        }
        // 遍历所有属性并打印
        feature.getProperties().forEach(property -> {
            // 避免打印几何对象（WKT很长）
            property.getUserData();
            if (!(property.getValue() instanceof Geometry)) {
                System.out.println(property.getName() + ": " + property.getValue());
            }
        });
    }

    public static void main(String[] args) {
        try {
            // 参数：Shapefile文件路径
            String shapefilePath = "/Users/<USER>/Downloads/district.shp";

            // 示例坐标（上海市中心坐标）
            double lng = 121.4737;
            double lat = 31.2304;

            System.out.println("\n=== 使用STRtree空间索引优化的行政区划查询 ===");
            AdminDivisionQuery queryTool = new AdminDivisionQuery(shapefilePath);

            // 打印索引统计信息
            queryTool.buildSpatialIndex();

            // 性能测试：比较传统方法和索引优化方法
            System.out.println("\n=== 性能测试 ===");

            // 测试传统方法
            long startTime = System.currentTimeMillis();
            SimpleFeature resultTraditional = queryTool.queryByCoordinateTraditional(lng, lat);
            long traditionalTime = System.currentTimeMillis() - startTime;
            System.out.println("传统方法查询耗时: " + traditionalTime + "ms");

            // 测试索引优化方法
            startTime = System.currentTimeMillis();
            SimpleFeature resultOptimized = queryTool.queryByCoordinate(lng, lat);
            long optimizedTime = System.currentTimeMillis() - startTime;
            System.out.println("索引优化方法查询耗时: " + optimizedTime + "ms");

            // 打印查询结果
            System.out.println("\n=== 查询结果 ===");
            queryTool.printFeatureInfo(resultOptimized);

            // 批量查询测试
            System.out.println("\n=== 批量查询测试 ===");
            double[][] testCoordinates = {
                {121.4737, 31.2304}, // 上海
                {116.4074, 39.9042}, // 北京
                {113.2644, 23.1291}, // 广州
                {114.0579, 22.5431}, // 深圳
                {120.1614, 30.2936}  // 杭州
            };

            startTime = System.currentTimeMillis();
            SimpleFeature[] batchResults = queryTool.batchQuery(testCoordinates);
            long batchTime = System.currentTimeMillis() - startTime;
            System.out.println("批量查询5个坐标耗时: " + batchTime + "ms");

            for (int i = 0; i < batchResults.length; i++) {
                System.out.println("坐标 [" + testCoordinates[i][0] + ", " + testCoordinates[i][1] + "] 查询结果:");
                if (batchResults[i] != null) {
                    System.out.println("  找到行政区划");
                } else {
                    System.out.println("  未找到对应的行政区划");
                }
            }

            queryTool.dispose();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}