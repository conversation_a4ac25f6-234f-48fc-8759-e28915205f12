package cn.hanyi.survey.service;

import org.opengis.feature.simple.SimpleFeature;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.HashMap;
import java.util.Map;

/**
 * 行政区划查询服务
 * 基于STRtree空间索引优化的Spring服务
 */
@Service
public class AdminDivisionService {

    @Value("${admin.division.shapefile.path:/data/shapefiles/district.shp}")
    private String shapefilePath;

    @Value("${admin.division.shapefile.charset:UTF-8}")
    private String charset;

    private AdminDivisionQuery queryTool;

    @PostConstruct
    public void init() {
        try {
            this.queryTool = new AdminDivisionQuery(shapefilePath, charset);
            System.out.println("AdminDivisionService initialized with shapefile: " + shapefilePath);
        } catch (Exception e) {
            System.err.println("Failed to initialize AdminDivisionService: " + e.getMessage());
            throw new RuntimeException("Failed to initialize AdminDivisionService", e);
        }
    }

    @PreDestroy
    public void destroy() {
        if (queryTool != null) {
            queryTool.dispose();
            System.out.println("AdminDivisionService disposed");
        }
    }

    /**
     * 根据经纬度查询行政区划信息
     *
     * @param longitude 经度
     * @param latitude  纬度
     * @return 行政区划信息Map，包含各级行政区划名称
     */
    public Map<String, Object> queryAdminDivision(double longitude, double latitude) {
        try {
            SimpleFeature feature = queryTool.queryByCoordinate(longitude, latitude);
            if (feature == null) {
                return null;
            }
            
            return extractAdminInfo(feature);
        } catch (Exception e) {
            System.err.println("Error querying admin division for coordinates [" + longitude + ", " + latitude + "]: " + e.getMessage());
            return null;
        }
    }

    /**
     * 批量查询行政区划信息
     *
     * @param coordinates 坐标数组，每个元素包含[longitude, latitude]
     * @return 查询结果数组，与输入坐标一一对应
     */
    public Map<String, Object>[] batchQueryAdminDivision(double[][] coordinates) {
        try {
            SimpleFeature[] features = queryTool.batchQuery(coordinates);
            Map<String, Object>[] results = new Map[features.length];
            
            for (int i = 0; i < features.length; i++) {
                if (features[i] != null) {
                    results[i] = extractAdminInfo(features[i]);
                } else {
                    results[i] = null;
                }
            }
            
            return results;
        } catch (Exception e) {
            System.err.println("Error in batch query: " + e.getMessage());
            return new Map[coordinates.length]; // 返回空数组
        }
    }

    /**
     * 从SimpleFeature中提取行政区划信息
     *
     * @param feature SimpleFeature对象
     * @return 包含行政区划信息的Map
     */
    private Map<String, Object> extractAdminInfo(SimpleFeature feature) {
        Map<String, Object> adminInfo = new HashMap<>();
        
        // 根据实际的Shapefile字段名称提取信息
        // 这些字段名可能需要根据具体的Shapefile数据调整
        try {
            // 常见的行政区划字段名
            String[] possibleProvinceFields = {"PROVINCE", "省", "省份", "PROV_NAME", "省名"};
            String[] possibleCityFields = {"CITY", "市", "城市", "CITY_NAME", "市名"};
            String[] possibleDistrictFields = {"DISTRICT", "区", "县", "区县", "DIST_NAME", "区县名"};
            String[] possibleCodeFields = {"CODE", "ADCODE", "行政代码", "代码"};
            String[] possibleNameFields = {"NAME", "名称", "FULL_NAME", "全名"};

            // 提取省份信息
            String province = extractFieldValue(feature, possibleProvinceFields);
            if (province != null) {
                adminInfo.put("province", province);
            }

            // 提取城市信息
            String city = extractFieldValue(feature, possibleCityFields);
            if (city != null) {
                adminInfo.put("city", city);
            }

            // 提取区县信息
            String district = extractFieldValue(feature, possibleDistrictFields);
            if (district != null) {
                adminInfo.put("district", district);
            }

            // 提取行政代码
            String code = extractFieldValue(feature, possibleCodeFields);
            if (code != null) {
                adminInfo.put("code", code);
            }

            // 提取完整名称
            String name = extractFieldValue(feature, possibleNameFields);
            if (name != null) {
                adminInfo.put("name", name);
            }

            // 如果没有找到任何标准字段，则返回所有非几何属性
            if (adminInfo.isEmpty()) {
                feature.getProperties().forEach(property -> {
                    if (!(property.getValue() instanceof org.locationtech.jts.geom.Geometry)) {
                        adminInfo.put(property.getName().getLocalPart(), property.getValue());
                    }
                });
            }

        } catch (Exception e) {
            System.err.println("Error extracting admin info: " + e.getMessage());
        }

        return adminInfo;
    }

    /**
     * 从Feature中提取指定字段的值
     *
     * @param feature SimpleFeature对象
     * @param fieldNames 可能的字段名数组
     * @return 字段值，如果未找到则返回null
     */
    private String extractFieldValue(SimpleFeature feature, String[] fieldNames) {
        for (String fieldName : fieldNames) {
            try {
                Object value = feature.getAttribute(fieldName);
                if (value != null) {
                    return value.toString().trim();
                }
            } catch (Exception e) {
                // 字段不存在，继续尝试下一个
            }
        }
        return null;
    }

    /**
     * 获取服务状态信息
     *
     * @return 服务状态Map
     */
    public Map<String, Object> getServiceStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("initialized", queryTool != null);
        status.put("shapefilePath", shapefilePath);
        status.put("charset", charset);
        
        if (queryTool != null) {
            try {
                // 这里可以添加更多状态信息
                status.put("indexBuilt", true); // 简化处理
            } catch (Exception e) {
                status.put("error", e.getMessage());
            }
        }
        
        return status;
    }

    /**
     * 测试查询功能
     *
     * @return 测试结果
     */
    public Map<String, Object> testQuery() {
        Map<String, Object> testResult = new HashMap<>();
        
        try {
            // 使用上海市中心坐标进行测试
            double testLng = 121.4737;
            double testLat = 31.2304;
            
            long startTime = System.currentTimeMillis();
            Map<String, Object> result = queryAdminDivision(testLng, testLat);
            long endTime = System.currentTimeMillis();
            
            testResult.put("testCoordinates", new double[]{testLng, testLat});
            testResult.put("queryTime", endTime - startTime);
            testResult.put("result", result);
            testResult.put("success", result != null);
            
        } catch (Exception e) {
            testResult.put("success", false);
            testResult.put("error", e.getMessage());
        }
        
        return testResult;
    }
}
